{"DA": "文档详细说明了在ZXR10 M6000-S路由器上通过BRAS配置PPPoEv4组播用户访问的步骤，包括网络接口设置、组播模板配置、VBUI接口启用PIM-SM及用户接入验证。重点在于组播模板与授权模板的绑定，以及通过命令行查看用户上线和组播路由信息。", "IA": "PPPoEv4组播配置包含四步骤：1.配置gei-0/6/0/2等网络接口地址；2.创建igmp service-profile 1组播模板并绑定ACL；3.在vbui1000接口启用PIM-SM；4.将组播模板关联至PPPoEv4授权模板。验证需通过show subscriber、show ip mroute和show ip igmp user等命令检查用户状态及组播路由表。", "SA": "**设备:** ZXR10 M6000-S路由器 **配置对象:** PPPoEv4组播用户 **核心步骤:** 1.网络接口配置（gei-0/6/0/2/********，loopback1/********） 2.组播模板绑定ACL（*********/*********） 3.VBUI接口启用PIM-SM（vbui1000/*********） 4.授权模板关联组播模板 **验证命令:** show subscriber（用户上线状态），show ip mroute（组播路由表），show ip igmp user（组播用户信息）"}