#!/usr/bin/env python
"""
摘要生成器运行脚本

此脚本提供了一个命令行界面，用于运行摘要生成器，
可以选择运行完整处理或测试模式。
"""

import argparse
import asyncio
import os
import sys


def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description="文本摘要生成器")
    parser.add_argument(
        "--test", 
        action="store_true", 
        help="运行测试模式，只处理少量文件"
    )
    parser.add_argument(
        "--limit", 
        type=int, 
        default=3, 
        help="测试模式下处理的文件数量限制（默认：3）"
    )
    parser.add_argument(
        "--source", 
        type=str, 
        default="structured_result", 
        help="源数据目录路径（默认：structured_result）"
    )
    parser.add_argument(
        "--target", 
        type=str, 
        default="structured_result_summary", 
        help="目标摘要目录路径（默认：structured_result_summary）"
    )
    parser.add_argument(
        "--concurrent", 
        type=int, 
        default=10, 
        help="最大并发请求数（默认：10）"
    )
    return parser.parse_args()


async def main():
    """
    主函数
    """
    args = parse_args()
    
    # 导入模块
    import summary_generator
    import test_summary_generator
    
    # 更新配置
    if args.source != "structured_result" or args.target != "structured_result_summary" or args.concurrent != 10:
        print(f"更新配置：源目录={args.source}, 目标目录={args.target}, 并发数={args.concurrent}")
        # 动态修改summary_generator.py中的配置
        summary_generator.SOURCE_DIR = args.source
        summary_generator.TARGET_DIR = args.target
        summary_generator.MAX_CONCURRENT_REQUESTS = args.concurrent
    
    # 确保目录结构正确创建
    print("确保目录结构完整...")
    await summary_generator.create_directory_structure()
    
    if args.test:
        print(f"运行测试模式，处理最多 {args.limit} 个文件...")
        # 运行测试脚本，传递文件限制参数
        await test_summary_generator.test_summary_generation(file_limit=args.limit)
    else:
        print("运行完整处理模式...")
        # 运行完整处理脚本
        await summary_generator.main()


if __name__ == "__main__":
    asyncio.run(main())