import os
import json
import asyncio
from pathlib import Path
from summary_generator import read_txt_file, generate_summary, save_summary, SOURCE_DIR, TARGET_DIR, create_directory_structure

# 测试配置
TEST_FILE_LIMIT = 3  # 限制测试的文件数量，可以通过run.py脚本修改

async def get_test_files(directory: str, limit: int):
    """
    获取指定数量的txt文件用于测试
    """
    test_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith(".txt"):
                test_files.append(os.path.join(root, file))
                if len(test_files) >= limit:
                    return test_files
    return test_files

async def test_summary_generation(file_limit=None):
    """
    测试摘要生成功能
    
    Args:
        file_limit: 可选的文件数量限制，如果提供则覆盖TEST_FILE_LIMIT
    """
    # 使用传入的file_limit或默认的TEST_FILE_LIMIT
    limit = file_limit if file_limit is not None else TEST_FILE_LIMIT
    print(f"开始测试摘要生成功能，将处理最多 {limit} 个文件")
    
    # 首先创建完整的目录结构
    print("创建目录结构...")
    await create_directory_structure()
    
    # 获取测试文件
    test_files = await get_test_files(SOURCE_DIR, limit)
    print(f"找到 {len(test_files)} 个测试文件")
    
    # 处理每个测试文件
    for file_path in test_files:
        print(f"\n处理文件: {file_path}")
        
        # 读取文件内容
        content = await read_txt_file(file_path)
        if not content:
            print(f"  文件内容为空，跳过")
            continue
        
        print(f"  文件内容长度: {len(content)} 字符")
        print(f"  内容预览: {content[:100]}...")
        
        # 生成摘要
        print("  生成摘要中...")
        summary = await generate_summary(content)
        
        if summary:
            print("  摘要生成成功:")
            print(f"  描述性摘要: {summary.get('DA', '未生成')}")
            print(f"  信息性摘要: {summary.get('IA', '未生成')}")
            print(f"  结构化摘要: {summary.get('SA', '未生成')}")
            
            # 保存摘要
            await save_summary(file_path, summary)
            
            # 计算目标路径
            rel_path = os.path.relpath(file_path, SOURCE_DIR)
            # 将.txt扩展名替换为.json
            base_path, _ = os.path.splitext(rel_path)
            json_path = f"{base_path}.json"
            target_path = os.path.join(TARGET_DIR, json_path)
            print(f"  摘要已保存到: {target_path}")
        else:
            print("  摘要生成失败")
    
    print("\n测试完成")

if __name__ == "__main__":
    # 如果直接运行此脚本，使用默认的TEST_FILE_LIMIT
    asyncio.run(test_summary_generation())