import os
import json
import asyncio
import httpx
import logging
import colorama
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from colorama import Fore, Style

# 初始化colorama
colorama.init(autoreset=True)

# 自定义彩色日志格式化器
class ColoredFormatter(logging.Formatter):
    COLORS = {
        'DEBUG': Fore.BLUE,
        'INFO': Fore.GREEN,
        'WARNING': Fore.YELLOW,
        'ERROR': Fore.RED,
        'CRITICAL': Fore.RED + Style.BRIGHT
    }

    def format(self, record):
        levelname = record.levelname
        if levelname in self.COLORS:
            record.levelname = f"{self.COLORS[levelname]}{levelname}{Style.RESET_ALL}"
            record.msg = f"{self.COLORS[levelname]}{record.msg}{Style.RESET_ALL}"
        return super().format(record)

# 配置日志
logger = logging.getLogger("summary_generator")
logger.setLevel(logging.INFO)

# 文件处理器
file_handler = logging.FileHandler("summary_generator.log",encoding='utf-8')
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(file_handler)

# 控制台处理器（带颜色）
console_handler = logging.StreamHandler()
console_handler.setFormatter(ColoredFormatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(console_handler)

# 配置常量
SOURCE_DIR = "structured_result"  # 源数据目录
TARGET_DIR = "structured_result_summary"  # 目标摘要目录
LLM_API_URL = "http://10.186.2.176:10010/CUCCAI-llm-hub/chat/completions"  # LLM API地址
API_KEY = "618149eb-d43e-4ddc-b406-b0c0e1efd281"  # API密钥
MAX_CONCURRENT_REQUESTS = 5  # 最大并发请求数
MAX_RETRIES = 5  # 最大重试次数
RETRY_DELAY = 5  # 重试延迟（秒）
LLM_NAME="Qwen3-32B" # deepseek_v3_int8_vpc

# 提示词模板
PROMPT_TEMPLATE = """
你是一个专业的文本摘要助手，你的任务是从我提供的文档中提取出一个简洁、准确的摘要。

请严格遵守以下规则：
1.  **摘要必须是独立成文的，能够让读者快速理解文档的核心内容，无需阅读全文。**
2.  **请返回三种不同格式的摘要，包括：描述性摘要，信息性摘要，结构化摘要 **
3.  **摘要长度控制在50至100字之间，并且经可能组织语言突出主题词。**
4.  **请保持客观，只总结原文内容，不要添加任何个人观点、评论或引申。**
5.  **摘要必须以纯文本形式返回，不要有任何多余的前缀、标题或结尾。**
6.  **将三种摘要的纯文本，放置在一个JSON中返回，{{DA:"描述性摘要",IA:"信息性摘要",SA:"结构化摘要"}}**

**文档内容:**
{content}

**示例:**
如果文档内容是"人工智能是研究、开发用于模拟、延伸和扩展人的智能的理论、方法、技术及应用系统的一门新的技术科学。它自诞生以来，已经发展出了机器学习、深度学习、自然语言处理等多个子领域。"
那么你应该返回：
{{
DA:"该文本介绍了人工智能（AI）的定义，将其描述为一门旨在模拟、扩展人类智能的新兴技术科学。文中提到了AI的几个主要子领域，包括机器学习、深度学习和自然语言处理。",
IA:"人工智能被定义为一门研究和开发模拟、延伸人类智能的理论、方法、技术及应用系统的新技术科学。自诞生以来，AI已发展出多个关键子领域，例如机器学习、深度学习和自然语言处理。",
SA:"**背景:** 人工智能（AI）是一门旨在模拟、延伸和扩展人类智能的新兴技术科学。**内容:** AI的研究和开发涵盖了理论、方法、技术和应用系统。其发展历程中已经形成了多个重要子领域。**子领域:** 目前，AI的主要子领域包括机器学习、深度学习和自然语言处理。"}}
"""


async def get_all_txt_files(directory: str) -> List[str]:
    """
    递归获取目录下所有的txt文件路径
    """
    txt_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith(".txt"):
                txt_files.append(os.path.join(root, file))
    return txt_files


async def read_txt_file(file_path: str) -> str:
    """
    读取txt文件内容
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except UnicodeDecodeError:
        # 尝试使用其他编码
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                return f.read()
        except Exception as e:
            logger.error(f"无法读取文件 {file_path}: {e}")
            return ""
    except Exception as e:
        logger.error(f"读取文件 {file_path} 时出错: {e}")
        return ""


async def generate_summary(content: str, retry_count: int = 0) -> Optional[Dict]:
    """
    使用LLM生成摘要，包含重试机制
    """
    if not content.strip():
        logger.warning("内容为空，跳过摘要生成")
        return None

    # 构建请求数据
    request_data = {
        "model":LLM_NAME,
        "messages": [
            {"role": "system", "content": "你是摘要提取助手"},
            {"role": "user", "content": PROMPT_TEMPLATE.format(content=content)}
        ],
        "stream": False,  # 设置为False以获取完整响应
        "temperature": 0.5,
        "max_tokens": 4000
    }

    try:
        # 设置代理
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                LLM_API_URL,
                headers={
                    'Authorization': f'Bearer {API_KEY}',
                    'Content-Type': 'application/json'
                },
                json=request_data
            )
            
            if response.status_code == 200:
                result = response.json()
                # 提取LLM返回的内容
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    # 尝试解析JSON格式的摘要
                    try:
                        # 查找JSON格式的内容
                        import re
                        json_match = re.search(r'\{\s*"?DA"?\s*:\s*".*"\s*,\s*"?IA"?\s*:\s*".*"\s*,\s*"?SA"?\s*:\s*".*"\s*\}', content)
                        if json_match:
                            json_str = json_match.group(0)
                            # 修复可能的JSON格式问题
                            json_str = json_str.replace("DA:", '"DA":')
                            json_str = json_str.replace("IA:", '"IA":')
                            json_str = json_str.replace("SA:", '"SA":')
                            # 确保键值对使用双引号
                            json_str = re.sub(r'([{,])\s*(\w+)\s*:', r'\1"\2":', json_str)
                            return json.loads(json_str)
                        else:
                            # 如果没有找到JSON格式，尝试构建一个
                            return {
                                "DA": "无法解析摘要格式",
                                "IA": "无法解析摘要格式",
                                "SA": "无法解析摘要格式"
                            }
                    except Exception as e:
                        logger.error(f"解析摘要JSON时出错: {e}")
                        return {
                            "DA": "解析摘要时出错",
                            "IA": "解析摘要时出错",
                            "SA": "解析摘要时出错"
                        }
                return None
            elif response.status_code == 429 and retry_count < MAX_RETRIES:
                # 请求过于频繁，触发熔断，进行重试
                retry_count += 1
                wait_time = RETRY_DELAY * retry_count
                logger.warning(f"请求过于频繁，等待 {wait_time} 秒后重试 (尝试 {retry_count}/{MAX_RETRIES})")
                await asyncio.sleep(wait_time)
                return await generate_summary(content, retry_count)
            else:
                logger.error(f"LLM API请求失败: {response.status_code} - {response.text}")
                if retry_count < MAX_RETRIES:
                    retry_count += 1
                    wait_time = RETRY_DELAY * retry_count
                    logger.warning(f"等待 {wait_time} 秒后重试 (尝试 {retry_count}/{MAX_RETRIES})")
                    await asyncio.sleep(wait_time)
                    return await generate_summary(content, retry_count)
                return None
    except Exception as e:
        logger.error(f"生成摘要时出错: {e}")
        if retry_count < MAX_RETRIES:
            retry_count += 1
            wait_time = RETRY_DELAY * retry_count
            logger.warning(f"等待 {wait_time} 秒后重试 (尝试 {retry_count}/{MAX_RETRIES})")
            await asyncio.sleep(wait_time)
            return await generate_summary(content, retry_count)
        return None


async def save_summary(source_path: str, summary: Dict) -> None:
    """
    保存摘要到目标目录，保持相同的目录结构，使用.json扩展名
    """
    if not summary:
        logger.warning(f"摘要为空，跳过保存: {source_path}")
        return

    try:
        # 计算目标路径
        rel_path = os.path.relpath(source_path, SOURCE_DIR)
        # 将.txt扩展名替换为.json
        base_path, _ = os.path.splitext(rel_path)
        json_path = f"{base_path}.json"
        target_path = os.path.join(TARGET_DIR, json_path)
        
        # 确保目标目录存在（使用pathlib创建完整的目录结构）
        target_dir = os.path.dirname(target_path)
        Path(target_dir).mkdir(parents=True, exist_ok=True)
        
        # 检查目录是否成功创建
        if not os.path.exists(target_dir):
            logger.error(f"无法创建目录: {target_dir}")
            return
        
        # 将摘要写入JSON文件
        with open(target_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        logger.info(f"摘要已保存到: {target_path}")
    except Exception as e:
        logger.error(f"保存摘要到文件时出错: {e}, 源文件: {source_path}")
        # 记录更详细的错误信息
        import traceback
        logger.debug(f"错误详情: {traceback.format_exc()}")



async def process_file(file_path: str, semaphore: asyncio.Semaphore) -> None:
    """
    处理单个文件：读取内容，生成摘要，保存结果
    """
    async with semaphore:
        logger.info(f"处理文件: {file_path}")
        content = await read_txt_file(file_path)
        if content:
            summary = await generate_summary(content)
            if summary:
                await save_summary(file_path, summary)
        else:
            logger.warning(f"文件内容为空，跳过: {file_path}")


async def create_directory_structure():
    """
    在目标目录中创建与源目录相同的目录结构
    """
    logger.info(f"开始创建目录结构: 从 {SOURCE_DIR} 到 {TARGET_DIR}")
    # 确保目标目录存在
    os.makedirs(TARGET_DIR, exist_ok=True)
    
    # 遍历源目录中的所有子目录
    for root, dirs, _ in os.walk(SOURCE_DIR):
        for dir_name in dirs:
            # 计算相对路径
            source_dir = os.path.join(root, dir_name)
            rel_dir = os.path.relpath(source_dir, SOURCE_DIR)
            target_dir = os.path.join(TARGET_DIR, rel_dir)
            
            # 创建目标目录
            os.makedirs(target_dir, exist_ok=True)
            logger.debug(f"创建目录: {target_dir}")
    
    logger.info(f"目录结构创建完成")

async def main():
    # 首先创建完整的目录结构
    await create_directory_structure()
    
    # 获取所有txt文件
    logger.info(f"开始扫描目录: {SOURCE_DIR}")
    txt_files = await get_all_txt_files(SOURCE_DIR)
    logger.info(f"找到 {len(txt_files)} 个txt文件")
    
    # 创建信号量控制并发
    semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)
    
    # 创建任务列表
    tasks = [process_file(file_path, semaphore) for file_path in txt_files]

    
    # 执行所有任务
    logger.info("开始处理文件...")
    
    await asyncio.gather(*tasks)
    logger.info("所有文件处理完成")


if __name__ == "__main__":
    asyncio.run(main())